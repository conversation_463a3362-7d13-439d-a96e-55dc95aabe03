/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

import { localize } from '../../../../nls.js';
import { $, append, getActiveWindow } from '../../../../base/browser/dom.js';
import { IDialogService } from '../../../../platform/dialogs/common/dialogs.js';
import './extensionFailureDialog.css';
import { IImportExtensionInfo } from '../../../../platform/extensionsSync/browser/extensionsSyncService.js';


// 方法1：使用自定义Modal对话框 - 完全自由的HTML控制
export function showExtensionFailureDialog(_dialogService: IDialogService, failedExtensions: IImportExtensionInfo[]): void {
	showCustomModalDialog(failedExtensions);
}

// 实现方法1：自定义Modal对话框
function showCustomModalDialog(failedExtensions: IImportExtensionInfo[]): void {
	const activeWindow = getActiveWindow();
	// 找到monaco-workbench容器，确保VSCode主题token生效
	const workbenchContainer = activeWindow.document.querySelector('.monaco-workbench') as HTMLElement;
	if (!workbenchContainer) {
		console.error('Could not find .monaco-workbench container');
		return;
	}

	// 创建遮罩层
	const overlay = append(workbenchContainer, $('.extension-failure-overlay'));

	// 创建对话框容器
	const dialog = append(overlay, $('.extension-failure-dialog'));

	// 创建标题栏
	const header = append(dialog, $('.dialog-header'));

	const titleContainer = append(header, $('.title-container'));

	const errorIcon = append(titleContainer, $('.error-icon'));
	errorIcon.classList.add('codicon', 'codicon-error');

	const title = append(titleContainer, $('.dialog-title'));
	title.textContent = localize('importFailureTitle', "Import Failed");

	const closeButton = append(header, $('.close-button'));
	closeButton.classList.add('codicon', 'codicon-close');

	// 创建内容区域
	const content = append(dialog, $('.dialog-content'));

	const description = append(content, $('.description'));

	// Create the first part of the text
	const textPart = document.createTextNode(localize('extensionImportFailureText', "Failed to import {0} extensions. ", failedExtensions.length));
	description.appendChild(textPart);

	// Create the clickable link
	const link = append(description, $('a.solution-link')) as HTMLAnchorElement;
	link.textContent = localize('extensionImportFailureLinkText', "Click to view solutions");
	link.href = 'https://docs.corp.kuaishou.com/d/home/<USER>';
	link.target = '_blank';
	link.rel = 'noopener noreferrer';

	// Add period after the link
	const periodPart = document.createTextNode('.');
	description.appendChild(periodPart);

	// 创建扩展列表
	const extensionList = append(content, $('.extension-list'));

	failedExtensions.forEach((ext) => {
		const item = append(extensionList, $('.extension-item'));

		// 扩展图标
		const icon = append(item, $('img.extension-icon')) as HTMLImageElement;
		const defaultIcon = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHBhdGggZD0iTTEzLjUgMTFMMTEuNSA5VjdIMTMuNVY1SDExLjVWM0g5LjVWNUg3LjVWM0g1LjVWNUgzLjVWN0g1LjVWOUgzLjVWMTFINS41VjEzSDcuNVYxMUg5LjVWMTNIMTEuNVYxMUgxMy41WiIgZmlsbD0iIzQyNDI0MiIvPgo8L3N2Zz4K';
		icon.src = ext.iconUrl || defaultIcon;
		icon.alt = ext.displayName;
		icon.title = ext.displayName;
		// Fallback to default icon if the extension icon fails to load
		icon.onerror = () => {
			icon.src = defaultIcon;
		};

		// 内容区域
		const itemContent = append(item, $('.item-content'));

		// 扩展名称
		const name = append(itemContent, $('.extension-name'));
		name.textContent = ext.displayName;

		// 错误信息
		const error = append(itemContent, $('.extension-error'));
		error.textContent = ext.error;

		// 错误图标
		const errorIcon = append(item, $('.item-error-icon'));
		errorIcon.classList.add('codicon', 'codicon-close');
	});

	// 关闭对话框的函数
	const closeDialog = () => {
		overlay.remove();
		activeWindow.document.removeEventListener('keydown', handleKeyDown);
	};

	// 绑定事件 - 只有关闭按钮可以关闭，不允许点击overlay关闭
	closeButton.addEventListener('click', closeDialog);

	// ESC键关闭
	const handleKeyDown = (e: KeyboardEvent) => {
		if (e.key === 'Escape') {
			closeDialog();
		}
	};
	activeWindow.document.addEventListener('keydown', handleKeyDown);
}

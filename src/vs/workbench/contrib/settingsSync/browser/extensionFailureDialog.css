/*---------------------------------------------------------------------------------------------
 *  Copyright (c) Microsoft Corporation. All rights reserved.
 *  Licensed under the MIT License. See License.txt in the project root for license information.
 *--------------------------------------------------------------------------------------------*/

/* Extension Failure Dialog Styles */
.extension-failure-overlay {
	position: fixed;
	top: 0;
	left: 0;
	width: 100%;
	height: 100%;
	background: rgba(0, 0, 0, 0.5);
	z-index: 10000;
	display: flex;
	align-items: center;
	justify-content: center;
}

.extension-failure-dialog {
	background: var(--vscode-editor-background);
	border: 1px solid var(--vscode-widget-border);
	border-radius: 6px;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
	width: 650px;
	max-width: 90vw;
	max-height: 80vh;
	display: flex;
	flex-direction: column;
	overflow: hidden;
}

.dialog-header {
	padding: 20px 24px 16px 24px;
	border-bottom: 1px solid var(--vscode-widget-border);
	display: flex;
	align-items: center;
	justify-content: space-between;
	background: var(--vscode-editor-background);
}

.title-container {
	display: flex;
	align-items: center;
	gap: 10px;
}

.error-icon {
	color: var(--vscode-errorForeground);
	font-size: 18px;
}

.dialog-title {
	font-size: 16px;
	font-weight: 500;
	color: var(--vscode-foreground);
	margin: 0;
}

.close-button {
	color: var(--vscode-foreground);
	font-size: 16px;
	cursor: pointer;
	padding: 4px;
	border-radius: 3px;
	background: transparent;
	border: none;
}

.close-button:hover {
	background: var(--vscode-toolbar-hoverBackground);
}

.dialog-content {
	padding: 0 24px 24px 24px;
	flex: 1;
	overflow-y: auto;
}

.description {
	color: var(--vscode-descriptionForeground);
	font-size: 14px;
	line-height: 1.4;
	margin-bottom: 20px;
}

.extension-list {
	border: 1px solid var(--vscode-input-border);
	border-radius: 6px;
	background: var(--vscode-editor-background);
	max-height: 400px;
	overflow-y: auto;
}

.extension-item {
	display: flex;
	align-items: flex-start;
	padding: 16px 20px;
	gap: 12px;
	transition: background-color 0.1s ease;
}

.extension-item:not(:last-child) {
	border-bottom: 1px solid var(--vscode-input-border);
}

.extension-item:hover {
	background: var(--vscode-list-hoverBackground);
}

.extension-icon {
	width: 18px;
	height: 18px;
	margin-top: 1px;
	flex-shrink: 0;
	border-radius: 2px;
	object-fit: cover;
}

.item-content {
	flex: 1;
	min-width: 0;
}

.extension-name {
	font-weight: 400;
	color: var(--vscode-foreground);
	font-size: 14px;
	margin-bottom: 6px;
	word-break: break-all;
}

.extension-error {
	color: var(--vscode-errorForeground);
	font-size: 13px;
	line-height: 1.4;
	word-break: break-word;
	opacity: 0.9;
}

.item-error-icon {
	color: var(--vscode-errorForeground);
	font-size: 18px;
	margin-top: 1px;
	flex-shrink: 0;
}



